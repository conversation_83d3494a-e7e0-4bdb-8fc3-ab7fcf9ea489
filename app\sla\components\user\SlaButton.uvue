<template>
  <view
    class="sla-button"
    :class="[
      `sla-button--${type}`,
      `sla-button--${size}`,
      { 'sla-button--disabled': disabled, 'sla-button--loading': loading }
    ]"
    :hover-class="disabled || loading ? '' : 'sla-button--hover'"
    @click="handleClick"
  >
    <view v-if="loading" class="sla-button__loading">
      <view class="sla-button__loading-indicator"></view>
    </view>
    <text class="sla-button__text">{{ text }}</text>
  </view>
</template>

<script>
export default {
  name: 'SlaButton',
  props: {
    // 按钮文字
    text: {
      type: String,
      default: '按钮'
    },
    // 按钮类型：primary, secondary, outline, text
    type: {
      type: String,
      default: 'primary'
    },
    // 按钮大小：large, medium, small
    size: {
      type: String,
      default: 'medium'
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否加载中
    loading: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleClick(e) {
      if (!this.disabled && !this.loading) {
        this.$emit('click', e);
      }
    }
  }
}
</script>

<style>
.sla-button {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  transition: all 0.3s;
}

/* 按钮类型 */
.sla-button--primary {
  background-color: #80B8F5;
  color: #FFFFFF;
}

.sla-button--secondary {
  background-color: #F79F77;
  color: #FFFFFF;
}

.sla-button--outline {
  background-color: transparent;
  border: 1px solid #80B8F5;
}

.sla-button--outline .sla-button__text {
  color: #80B8F5;
}

.sla-button--text {
  background-color: transparent;
}

.sla-button--text .sla-button__text {
  color: #80B8F5;
}

/* 按钮大小 */
.sla-button--large {
  height: 48px;
  padding: 0 24px;
}

.sla-button--large .sla-button__text {
  font-size: 18px;
}

.sla-button--medium {
  height: 40px;
  padding: 0 16px;
}

.sla-button--medium .sla-button__text {
  font-size: 16px;
}

.sla-button--small {
  height: 32px;
  padding: 0 12px;
}

.sla-button--small .sla-button__text {
  font-size: 14px;
}

/* 按钮状态 */
.sla-button--disabled {
  opacity: 0.5;
}

.sla-button--hover {
  opacity: 0.8;
  transform: translateY(1px);
}

/* 加载状态 */
.sla-button--loading {
  opacity: 0.8;
}

.sla-button__loading {
  margin-right: 8px;
}

.sla-button__loading-indicator {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: #FFFFFF;
  border-radius: 999px;
  animation: sla-button-spin 1s linear infinite;
}

.sla-button--outline .sla-button__loading-indicator,
.sla-button--text .sla-button__loading-indicator {
  border: 2px solid rgba(128, 184, 245, 0.3);
  border-top-color: #80B8F5;
}

.sla-button__text {
  font-weight: bold;
  text-align: center;
}

/* 加载动画 */
@keyframes sla-button-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>