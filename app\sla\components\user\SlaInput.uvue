<template>
  <view class="sla-input" :class="{ 'sla-input--error': error, 'sla-input--disabled': disabled }">
    <view class="sla-input__label" v-if="label">
      <text class="sla-input__label-text">{{ label }}</text>
      <text class="sla-input__required" v-if="required">*</text>
    </view>
    <view class="sla-input__wrapper">
      <view class="sla-input__prefix" v-if="$slots.prefix">
        <slot name="prefix"></slot>
      </view>
      <input
        class="sla-input__field"
        :type="actualType"
        :value="value"
        :placeholder="placeholder"
        :password="type === 'password' && !passwordVisible"
        :disabled="disabled"
        :maxlength="maxlength"
        :focus="focus"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
      />
      <view class="sla-input__suffix" v-if="$slots.suffix || clearable || type === 'password'">
        <view class="sla-input__clear" v-if="clearable && value && !disabled" @click="handleClear">
          <text class="sla-input__clear-icon">×</text>
        </view>
        <view class="sla-input__password-toggle" v-if="type === 'password'" @click="togglePasswordVisible">
          <text class="sla-input__password-icon">{{ passwordVisible ? '👁' : '🙈' }}</text>
        </view>
        <slot name="suffix"></slot>
      </view>
    </view>
    <view class="sla-input__error" v-if="error">
      <text class="sla-input__error-text">{{ error }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'SlaInput',
  props: {
    // 输入框值
    value: {
      type: String,
      default: ''
    },
    // 输入框类型
    type: {
      type: String,
      default: 'text'
    },
    // 输入框标签
    label: {
      type: String,
      default: ''
    },
    // 输入框占位符
    placeholder: {
      type: String,
      default: '请输入'
    },
    // 是否必填
    required: {
      type: Boolean,
      default: false
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 最大长度
    maxlength: {
      type: Number,
      default: -1
    },
    // 是否可清除
    clearable: {
      type: Boolean,
      default: false
    },
    // 错误信息
    error: {
      type: String,
      default: ''
    },
    // 是否自动聚焦
    focus: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      passwordVisible: false
    };
  },
  computed: {
    // 实际输入类型
    actualType() {
      if (this.type === 'password' && this.passwordVisible) {
        return 'text';
      }
      return this.type;
    }
  },
  methods: {
    // 处理输入事件
    handleInput(e) {
      this.$emit('input', e.detail.value);
    },
    // 处理聚焦事件
    handleFocus(e) {
      this.$emit('focus', e);
    },
    // 处理失焦事件
    handleBlur(e) {
      this.$emit('blur', e);
    },
    // 处理清除事件
    handleClear() {
      this.$emit('input', '');
      this.$emit('clear');
    },
    // 切换密码可见性
    togglePasswordVisible() {
      this.passwordVisible = !this.passwordVisible;
    }
  }
}
</script>

<style>
.sla-input {
  margin-bottom: 16px;
}

.sla-input__label {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 8px;
}

.sla-input__label-text {
  font-size: 14px;
  color: #333333;
  font-weight: bold;
}

.sla-input__required {
  color: #DD524D;
  margin-left: 4px;
}

.sla-input__wrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  background-color: #FFFFFF;
  padding: 0 12px;
  height: 44px;
}

.sla-input--error .sla-input__wrapper {
  border-color: #DD524D;
}

.sla-input--disabled .sla-input__wrapper {
  background-color: #F8F9FA;
  opacity: 0.7;
}

.sla-input__prefix {
  margin-right: 8px;
}

.sla-input__field {
  flex: 1;
  height: 100%;
  font-size: 16px;
  color: #333333;
}

.sla-input__suffix {
  margin-left: 8px;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.sla-input__clear, .sla-input__password-toggle {
  padding: 4px;
}

.sla-input__clear-icon {
  font-size: 18px;
  color: #999999;
}

.sla-input__password-icon {
  font-size: 18px;
  color: #999999;
}

.sla-input__error {
  margin-top: 4px;
}

.sla-input__error-text {
  font-size: 12px;
  color: #DD524D;
}
</style>

