<template>
  <view class="sla-countdown" @click="handleClick" :class="{ 'sla-countdown--disabled': counting || disabled }">
    <text class="sla-countdown__text" :class="{ 'sla-countdown__text--disabled': counting || disabled }">
      {{ buttonText }}
    </text>
  </view>
</template>

<script lang="ts">
export default {
  name: 'SlaCountdown',
  props: {
    // 初始文本
    initialText: {
      type: String,
      default: '获取验证码'
    },
    // 倒计时文本，{n}会被替换为剩余秒数
    countingText: {
      type: String,
      default: '{n}秒后重新获取'
    },
    // 倒计时结束后的文本
    finishText: {
      type: String,
      default: '重新获取'
    },
    // 倒计时时长（秒）
    seconds: {
      type: Number,
      default: 60
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      counting: false as boolean,
      remainingSeconds: this.seconds as number
    };
  },
  computed: {
    buttonText(): string {
      if (!this.counting) {
        return this.remainingSeconds === this.seconds ? this.initialText : this.finishText;
      }
      return this.countingText.replace('{n}', this.remainingSeconds.toString());
    }
  },
  methods: {
    // 处理点击事件
    handleClick(): void {
      console.log('SlaCountdown 点击事件触发');
      console.log('当前状态 - 倒计时中:', this.counting, '禁用状态:', this.disabled);

      if (this.counting || this.disabled) {
        console.log('按钮被禁用或正在倒计时，不触发点击事件');
        return;
      }

      console.log('发送点击事件');
      this.$emit('click');
    },
    // 开始倒计时
    start(): void {
      if (this.counting) {
        return;
      }

      this.counting = true;
      this.remainingSeconds = this.seconds;

      this.countdown();
    },
    // 倒计时逻辑
    countdown(): void {
      if (this.remainingSeconds <= 0) {
        this.counting = false;
        return;
      }

      setTimeout(() => {
        this.remainingSeconds--;
        this.countdown();
      }, 1000);
    },
    // 重置倒计时
    reset(): void {
      this.counting = false;
      this.remainingSeconds = this.seconds;
    }
  }
}
</script>

<style>
.sla-countdown {
  padding: 0 12px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  background-color: rgba(128, 184, 245, 0.1);
  transition-property: transform, opacity;
  transition-duration: 0.3s;
  transition-timing-function: ease;
}

.sla-countdown:active {
  background-color: rgba(128, 184, 245, 0.2);
}

.sla-countdown__text {
  font-size: 14px;
  color: #80B8F5;
  font-weight: bold;
}

.sla-countdown__text--disabled {
  color: #999999;
}

.sla-countdown--disabled {
  pointer-events: none;
  background-color: rgba(0, 0, 0, 0.05);
  opacity: 0.7;
}
</style>