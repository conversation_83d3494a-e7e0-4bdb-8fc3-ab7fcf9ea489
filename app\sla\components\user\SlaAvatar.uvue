<template>
  <view class="sla-avatar" :class="[`sla-avatar--${size}`]" @click="handleClick">
    <image 
      class="sla-avatar__image" 
      :src="src || defaultAvatar" 
      :mode="mode"
    ></image>
    <view class="sla-avatar__edit" v-if="editable">
      <text class="sla-avatar__edit-icon">✏️</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'SlaAvatar',
  props: {
    // 头像图片地址
    src: {
      type: String,
      default: ''
    },
    // 默认头像图片地址
    defaultAvatar: {
      type: String,
      default: '/static/images/default-avatar.png'
    },
    // 头像大小：large, medium, small
    size: {
      type: String,
      default: 'medium'
    },
    // 图片裁剪模式
    mode: {
      type: String,
      default: 'aspectFill'
    },
    // 是否可编辑
    editable: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    // 处理点击事件
    handleClick() {
      if (this.editable) {
        this.chooseImage();
      }
      this.$emit('click');
    },
    // 选择图片
    chooseImage() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          this.$emit('change', tempFilePath);
        }
      });
    }
  }
}
</script>

<style>
.sla-avatar {
  position: relative;
  border-radius: 999px;
  overflow: hidden;
}

.sla-avatar--large {
  width: 96px;
  height: 96px;
}

.sla-avatar--medium {
  width: 64px;
  height: 64px;
}

.sla-avatar--small {
  width: 40px;
  height: 40px;
}

.sla-avatar__image {
  width: 100%;
  height: 100%;
}

.sla-avatar__edit {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 24px;
  height: 24px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 999px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sla-avatar__edit-icon {
  font-size: 14px;
  color: #FFFFFF;
}
</style>
