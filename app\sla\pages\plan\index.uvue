<template>
  <view class="plan-container">
    <!-- 顶部导航栏 -->
    <sla-navbar title="学习计划" :show-back="false">
      <template #right>
        <view class="history-button" @click="navigateToHistory">
          <text class="history-button__text">历史</text>
        </view>
      </template>
    </sla-navbar>

    <!-- 主要内容 -->
    <scroll-view class="plan-content" scroll-y>
      <!-- 统计数据区域 -->
      <view class="plan-stats">
        <view class="stat-item">
          <text class="stat-value">{{planStats.totalPlans}}</text>
          <text class="stat-label">总计划</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{planStats.inProgressPlans || planStats.totalPlans - planStats.completedPlans}}</text>
          <text class="stat-label">进行中</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{planStats.completedPlans}}</text>
          <text class="stat-label">已完成</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{planStats.checkInCount || 0}}</text>
          <text class="stat-label">今日打卡</text>
        </view>
      </view>

      <!-- 计划标签页 -->
      <view class="plan-tabs">
        <view
          class="plan-tab"
          :class="{'plan-tab--active': activeTab === 'today'}"
          @click="switchTab('today')"
        >
          <text class="plan-tab__text">今日计划</text>
        </view>
        <view
          class="plan-tab"
          :class="{'plan-tab--active': activeTab === 'upcoming'}"
          @click="switchTab('upcoming')"
        >
          <text class="plan-tab__text">即将到来</text>
        </view>
        <view
          class="plan-tab"
          :class="{'plan-tab--active': activeTab === 'completed'}"
          @click="switchTab('completed')"
        >
          <text class="plan-tab__text">已完成</text>
        </view>
      </view>

      <!-- 计划列表区域 -->
      <view class="plan-list-container">
        <!-- 今日计划 -->
        <view class="plan-section" v-if="activeTab === 'today'">
          <view class="plan-list">
            <view v-if="todayPlans.length === 0" class="empty-state">
              <view class="empty-icon">📋</view>
              <text class="empty-title">暂无今日计划</text>
              <text class="empty-text">点击右下角"+"按钮添加计划</text>
            </view>
            <block v-else>
              <view
                v-for="(plan, index) in todayPlans"
                :key="plan.planId || index"
                class="plan-row"
                :class="{'plan-row--completed': plan.todayCompleted}"
                @click="viewPlanDetail(plan)"
              >
                <view
                  class="plan-row__checkbox"
                  :class="{'plan-row__checkbox--checked': plan.todayCompleted}"
                  @click.stop="plan.todayCompleted ? null : togglePlanStatus(plan)"
                >
                  <text class="plan-row__check-icon" v-if="plan.todayCompleted">✓</text>
                </view>
                <view class="plan-row__body">
                  <view class="plan-row__header">
                    <text class="plan-row__title" :class="{'plan-row__title--completed': plan.todayCompleted}">{{ plan.title }}</text>
                    <view class="plan-row__tags">
                      <text class="plan-row__tag" :class="'plan-row__tag--' + plan.priority">{{ getPriorityText(plan.priority) }}</text>
                      <text class="plan-row__tag plan-row__tag--type">{{ getPlanTypeText(plan.type) }}</text>
                    </view>
                  </view>
                  <view class="plan-row__info">
                    <text class="plan-row__date">{{ formatDate(plan.date) }}</text>
                    <text class="plan-row__time" v-if="getTimeRange(plan.subTasks)">{{ getTimeRange(plan.subTasks) }}</text>
                  </view>
                </view>
              </view>
            </block>
          </view>
        </view>

        <!-- 即将到来 -->
        <view class="plan-section" v-if="activeTab === 'upcoming'">
          <view class="plan-list">
            <view v-if="upcomingPlans.length === 0" class="empty-state">
              <view class="empty-icon">📅</view>
              <text class="empty-title">暂无即将到来的计划</text>
              <text class="empty-text">点击右下角"+"按钮添加计划</text>
            </view>
            <block v-else>
              <view
                v-for="(plan, index) in upcomingPlans"
                :key="plan.planId || index"
                class="plan-row"
                @click="viewPlanDetail(plan)"
              >
                <view
                  class="plan-row__checkbox"
                  :class="{'plan-row__checkbox--disabled': true}"
                >
                  <!-- 即将到来的计划不能打卡 -->
                </view>
                <view class="plan-row__body">
                  <view class="plan-row__header">
                    <text class="plan-row__title">{{ plan.title }}</text>
                    <view class="plan-row__tags">
                      <text class="plan-row__tag" :class="'plan-row__tag--' + plan.priority">{{ getPriorityText(plan.priority) }}</text>
                      <text class="plan-row__tag plan-row__tag--type">{{ getPlanTypeText(plan.type) }}</text>
                    </view>
                  </view>
                  <view class="plan-row__info">
                    <text class="plan-row__date">{{ formatDate(plan.date) }}</text>
                    <text class="plan-row__time" v-if="getTimeRange(plan.subTasks)">{{ getTimeRange(plan.subTasks) }}</text>
                  </view>
                </view>
              </view>
            </block>
          </view>
        </view>

        <!-- 已完成计划 -->
        <view class="plan-section" v-if="activeTab === 'completed'">
          <view class="plan-list">
            <view v-if="completedPlans.length === 0" class="empty-state">
              <view class="empty-icon">✅</view>
              <text class="empty-title">暂无已完成的计划</text>
              <text class="empty-text">完成计划后会在这里显示</text>
            </view>
            <block v-else>
              <view
                v-for="(plan, index) in completedPlans"
                :key="plan.planId || index"
                class="plan-row"
                :class="{'plan-row--completed': true}"
                @click="viewPlanDetail(plan)"
              >
                <view
                  class="plan-row__checkbox"
                  :class="{'plan-row__checkbox--checked': true}"
                >
                  <text class="plan-row__check-icon">✓</text>
                </view>
                <view class="plan-row__body">
                  <view class="plan-row__header">
                    <text class="plan-row__title" :class="{'plan-row__title--completed': true}">{{ plan.title }}</text>
                    <view class="plan-row__tags">
                      <text class="plan-row__tag" :class="'plan-row__tag--' + plan.priority">{{ getPriorityText(plan.priority) }}</text>
                      <text class="plan-row__tag plan-row__tag--type">{{ getPlanTypeText(plan.type) }}</text>
                    </view>
                  </view>
                  <view class="plan-row__info">
                    <text class="plan-row__date">{{ formatDate(plan.date) }}</text>
                    <text class="plan-row__time" v-if="getTimeRange(plan.subTasks)">{{ getTimeRange(plan.subTasks) }}</text>
                  </view>
                </view>
              </view>
            </block>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 浮动添加按钮 -->
    <view class="floating-button" @click="toggleAddMenu">
      <text class="floating-button__icon">+</text>
    </view>

    <!-- 添加菜单 -->
    <view class="add-menu" v-if="showAddMenu">
      <view class="add-menu-backdrop" @click="toggleAddMenu"></view>
      <view class="add-menu-content">
        <view class="add-menu-item" @click="handleAddPlan">
          <text class="add-menu-item__icon">+</text>
          <text class="add-menu-item__text">手动创建</text>
        </view>
        <view class="add-menu-item" @click="handleAiPlan">
          <text class="add-menu-item__icon">🤖</text>
          <text class="add-menu-item__text">AI生成</text>
        </view>
      </view>
    </view>

    <!-- 底部标签栏 -->
    <custom-tab-bar
      :current="1"
      :showAiToolbox="false"
      @centerClick="navigateToAI"
      @tabClick="handleTabClick"
    ></custom-tab-bar>

    <!-- 添加计划弹窗 -->
    <uni-popup ref="addPlanPopup" type="center">
      <view class="plan-modal">
        <view class="plan-modal__header">
          <text class="plan-modal__title">添加计划</text>
          <text class="plan-modal__close" @click="closeAddPlanModal">×</text>
        </view>

        <view class="plan-modal__body">
          <view class="form-item">
            <text class="form-label required">计划标题</text>
            <input class="form-input" v-model="planForm.title" placeholder="请输入计划标题" />
          </view>

          <view class="form-item">
            <text class="form-label required">计划类型</text>
            <view class="plan-type-selector">
              <view
                class="plan-type-item"
                :class="{'plan-type-item--active': planForm.type === 'daily'}"
                @click="planForm.type = 'daily'"
              >
                <text class="plan-type-item__text">日计划</text>
              </view>
              <view
                class="plan-type-item"
                :class="{'plan-type-item--active': planForm.type === 'weekly'}"
                @click="planForm.type = 'weekly'"
              >
                <text class="plan-type-item__text">周计划</text>
              </view>
              <view
                class="plan-type-item"
                :class="{'plan-type-item--active': planForm.type === 'monthly'}"
                @click="planForm.type = 'monthly'"
              >
                <text class="plan-type-item__text">月计划</text>
              </view>
            </view>
          </view>

          <view class="form-item">
            <text class="form-label required">日期</text>
            <picker mode="date" :value="planForm.date" @change="onPlanDateChange">
              <view class="picker-item">{{planForm.date || '请选择日期'}}</view>
            </picker>
          </view>

          <view class="form-item">
            <text class="form-label">优先级</text>
            <view class="priority-selector">
              <view
                class="priority-item"
                :class="{'priority-item--active': planForm.priority === 'low'}"
                @click="planForm.priority = 'low'"
              >
                <text class="priority-item__text">低</text>
              </view>
              <view
                class="priority-item priority-item--medium"
                :class="{'priority-item--active': planForm.priority === 'medium'}"
                @click="planForm.priority = 'medium'"
              >
                <text class="priority-item__text">中</text>
              </view>
              <view
                class="priority-item priority-item--high"
                :class="{'priority-item--active': planForm.priority === 'high'}"
                @click="planForm.priority = 'high'"
              >
                <text class="priority-item__text">高</text>
              </view>
            </view>
          </view>

          <view class="form-item">
            <text class="form-label">描述</text>
            <textarea class="form-textarea" v-model="planForm.description" placeholder="请输入计划描述"></textarea>
          </view>
        </view>

        <view class="plan-modal__footer">
          <view class="btn-cancel" hover-class="btn-hover" @click="closeAddPlanModal">取消</view>
          <view class="btn-primary" hover-class="btn-hover" @click="savePlanForm">保存</view>
        </view>
      </view>
    </uni-popup>

    <!-- AI生成计划 -->
    <uni-popup ref="aiPlanPopup" type="center">
      <view class="plan-modal">
        <view class="plan-modal__header ai-header">
          <text class="plan-modal__title">AI生成计划</text>
          <text class="plan-modal__close" @click="closeAiPlanModal">×</text>
        </view>

        <view class="plan-modal__body">
          <view class="ai-intro">
            <text class="ai-intro__text">AI将根据您的需求生成个性化学习计划</text>
            <text class="ai-intro__note">请填写AI生成计划需要3-5分钟时间</text>
          </view>

          <view class="form-item">
            <text class="form-label required">学习目标</text>
            <textarea class="form-textarea" v-model="aiPlanForm.description" placeholder="请详细描述您的学习目标和要求"></textarea>
          </view>

          <view class="form-item">
            <text class="form-label required">开始日期</text>
            <picker mode="date" :value="aiPlanForm.startDate" @change="onStartDateChange">
              <view class="picker-item">{{aiPlanForm.startDate || '请选择日期'}}</view>
            </picker>
          </view>

          <view class="form-item">
            <text class="form-label">学习风格</text>
            <view class="learning-style-selector">
              <view
                class="learning-style-item"
                :class="{'learning-style-item--active': aiPlanForm.learningStyle === 'visual'}"
                @click="selectLearningStyle('visual')"
              >
                <text class="learning-style-item__icon">👁️</text>
                <text class="learning-style-item__text">视觉型</text>
              </view>
              <view
                class="learning-style-item"
                :class="{'learning-style-item--active': aiPlanForm.learningStyle === 'auditory'}"
                @click="selectLearningStyle('auditory')"
              >
                <text class="learning-style-item__icon">👂</text>
                <text class="learning-style-item__text">听觉型</text>
              </view>
              <view
                class="learning-style-item"
                :class="{'learning-style-item--active': aiPlanForm.learningStyle === 'reading'}"
                @click="selectLearningStyle('reading')"
              >
                <text class="learning-style-item__icon">📖</text>
                <text class="learning-style-item__text">阅读型</text>
              </view>
              <view
                class="learning-style-item"
                :class="{'learning-style-item--active': aiPlanForm.learningStyle === 'kinesthetic'}"
                @click="selectLearningStyle('kinesthetic')"
              >
                <text class="learning-style-item__icon">🤲</text>
                <text class="learning-style-item__text">动手型</text>
              </view>
            </view>
          </view>

          <view class="form-item">
            <text class="form-label">技能水平</text>
            <view class="skill-level-selector">
              <text class="skill-level-label">初学</text>
              <slider min="1" max="5" :value="aiPlanForm.skillLevel" show-value @change="onSkillLevelChange"></slider>
              <text class="skill-level-label">专家</text>
            </view>
          </view>

          <view class="form-item">
            <text class="form-label">每日学习时间</text>
            <picker :range="studyTimeOptions" :value="studyTimeIndex" @change="onStudyTimeChange">
              <view class="picker-item">{{studyTimeOptions[studyTimeIndex]}}</view>
            </picker>
          </view>

          <view class="form-item">
            <text class="form-label">其他要求</text>
            <textarea class="form-textarea" v-model="aiPlanForm.otherRequirements" placeholder="其他特殊要求"></textarea>
          </view>
        </view>

        <view class="plan-modal__footer">
          <view class="btn-cancel" hover-class="btn-hover" @click="closeAiPlanModal">取消</view>
          <view class="btn-generate" hover-class="btn-hover" @click="generateAiPlan" :class="{'btn-disabled': isGeneratingPlan}">
            <text v-if="isGeneratingPlan">生成中...</text>
            <text v-else">生成计划</text>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 计划详情 -->
    <uni-popup ref="planDetailPopup" type="center">
      <view class="plan-detail-modal" v-if="selectedPlan">
        <view class="plan-detail-modal__header">
          <text class="plan-detail-modal__title">计划详情</text>
          <text class="plan-detail-modal__close" @click="closePlanDetailModal">×</text>
        </view>

        <scroll-view class="plan-detail-modal__body" scroll-y>
          <view class="plan-detail-header">
            <text class="plan-detail-title">{{selectedPlan.title}}</text>
            <text class="plan-detail-time">{{selectedPlan.startTime}} - {{selectedPlan.endTime}}</text>
            <text class="plan-detail-date" v-if="selectedPlan.date">{{formatDate(selectedPlan.date)}}</text>
            <text class="plan-detail-type" v-if="selectedPlan.aiGenerated">AI生成</text>
          </view>

          <view class="plan-detail-description" v-if="selectedPlan.description">
            <text class="section-title">计划描述</text>
            <text class="plan-detail-description__text">{{selectedPlan.description}}</text>
          </view>

          <view class="plan-detail-subtasks" v-if="selectedPlan.subTasks && selectedPlan.subTasks.length > 0">
            <text class="section-title">子任务</text>
            <view class="subtask-item" v-for="(subtask, index) in selectedPlan.subTasks" :key="index">
              <view class="subtask-checkbox" :class="{'subtask-checkbox--checked': subtask.completed}" @click="toggleSubtaskStatus(index)"></view>
              <view class="subtask-content">
                <text class="subtask-title" :class="{'subtask-title--completed': subtask.completed}">{{subtask.title}}</text>
                <text class="subtask-time" v-if="subtask.startTime && subtask.endTime">{{subtask.startTime}} - {{subtask.endTime}}</text>
                <text class="subtask-description" v-if="subtask.description">{{subtask.description}}</text>
                <text class="subtask-completion" v-if="subtask.completed">完成时间： {{formatDateTime(subtask.completionTime)}}</text>
              </view>
            </view>
          </view>

          <view class="resource-recommendations" v-if="selectedPlan.recommendedResources && selectedPlan.recommendedResources.length > 0">
            <text class="section-title">推荐资源</text>
            <view class="resource-list">
              <view class="resource-item" v-for="(resource, index) in selectedPlan.recommendedResources" :key="index" @click="openResource(resource)">
                <view class="resource-icon" :class="'resource-icon--' + resource.type">
                  <text class="resource-icon__text">{{getResourceTypeIcon(resource.type)}}</text>
                </view>
                <view class="resource-content">
                  <text class="resource-title">{{resource.title}}</text>
                  <text class="resource-desc">{{resource.description}}</text>
                  <view class="resource-meta">
                    <text class="resource-difficulty">难度: {{resource.difficulty}}/5</text>
                    <text class="resource-relevance">相关度： {{resource.relevance}}/5</text>
                  </view>
                </view>
                <text class="resource-arrow">></text>
              </view>
            </view>
          </view>
        </scroll-view>

        <view class="plan-detail-modal__footer">
          <view class="btn-cancel" hover-class="btn-hover" @click="closePlanDetailModal">关闭</view>
          <view class="btn-edit" hover-class="btn-hover" @click="showEditPlanModal" v-if="!selectedPlan.aiGenerated && selectedPlan.saved">编辑</view>
          <view class="btn-primary" hover-class="btn-hover" @click="savePlan" v-if="!selectedPlan.saved">保存计划</view>
        </view>
      </view>
    </uni-popup>

    <!-- 编辑计划 -->
    <uni-popup ref="editPlanPopup" type="center">
      <view class="plan-modal">
        <view class="plan-modal__header">
          <text class="plan-modal__title">编辑计划</text>
          <text class="plan-modal__close" @click="closeEditPlanModal">×</text>
        </view>

        <view class="plan-modal__body">
          <view class="form-item">
            <text class="form-label required">计划标题</text>
            <input class="form-input" v-model="editPlanForm.title" placeholder="请输入标题" />
          </view>

          <view class="form-item">
            <text class="form-label">计划类型</text>
            <view class="plan-type-selector">
              <view
                class="plan-type-item plan-type-item--disabled"
                :class="{'plan-type-item--active': editPlanForm.type === 'daily'}"
              >
                <text class="plan-type-item__text">日计划</text>
              </view>
              <view
                class="plan-type-item plan-type-item--disabled"
                :class="{'plan-type-item--active': editPlanForm.type === 'weekly'}"
              >
                <text class="plan-type-item__text">周计划</text>
              </view>
              <view
                class="plan-type-item plan-type-item--disabled"
                :class="{'plan-type-item--active': editPlanForm.type === 'monthly'}"
              >
                <text class="plan-type-item__text">月计划</text>
              </view>
            </view>
          </view>

          <view class="form-item">
            <text class="form-label required">日期</text>
            <picker mode="date" :value="editPlanForm.date" @change="onEditPlanDateChange">
              <view class="picker-item">{{editPlanForm.date || '请选择日期'}}</view>
            </picker>
          </view>

          <view class="form-item">
            <text class="form-label">优先级</text>
            <view class="priority-selector">
              <view
                class="priority-item priority-item--low"
                :class="{'priority-item--active': editPlanForm.priority === 'low'}"
                @click="editPlanForm.priority = 'low'"
              >
                <text class="priority-item__text">低</text>
              </view>
              <view
                class="priority-item priority-item--medium"
                :class="{'priority-item--active': editPlanForm.priority === 'medium'}"
                @click="editPlanForm.priority = 'medium'"
              >
                <text class="priority-item__text">中</text>
              </view>
              <view
                class="priority-item priority-item--high"
                :class="{'priority-item--active': editPlanForm.priority === 'high'}"
                @click="editPlanForm.priority = 'high'"
              >
                <text class="priority-item__text">高</text>
              </view>
            </view>
          </view>

          <view class="form-item">
            <text class="form-label">描述</text>
            <textarea class="form-textarea" v-model="editPlanForm.description" placeholder="请输入计划描述"></textarea>
          </view>
        </view>

        <view class="plan-modal__footer">
          <view class="btn-cancel" hover-class="btn-hover" @click="closeEditPlanModal">取消</view>
          <view class="btn-primary" hover-class="btn-hover" @click="updatePlanForm">保存</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import SlaNavbar from '../../components/user/SlaNavbar.uvue';
import CustomTabBar from '../../components/user/CustomTabBar.uvue';
import { getPlanList, getPlanStats, createPlan, updatePlan, deletePlan, completePlan, generateAiPlan } from '../../utils/api/plan.js';
import { formatDate, formatDateTime } from '../../utils/date.js';

export default {
  components: {
    SlaNavbar,
    CustomTabBar
  },
  data() {
    return {
      // 当前激活的标签页
      activeTab: 'today',

      // 计划统计数据
      planStats: {
        totalPlans: 0,
        inProgressPlans: 0,
        completedPlans: 0,
        checkInCount: 0
      },

      // 计划列表
      todayPlans: [],
      upcomingPlans: [],
      completedPlans: [],

      // 添加菜单显示状态
      showAddMenu: false,

      // 计划表单
      planForm: {
        title: '',
        type: 'daily',
        date: '',
        priority: 'medium',
        description: ''
      },

      // AI计划表单
      aiPlanForm: {
        description: '',
        startDate: '',
        learningStyle: '',
        skillLevel: 3,
        studyTime: '',
        otherRequirements: ''
      },

      // 编辑计划表单
      editPlanForm: {
        planId: '',
        title: '',
        type: 'daily',
        date: '',
        priority: 'medium',
        description: ''
      },

      // 选中的计划
      selectedPlan: null,

      // 学习时间选项
      studyTimeOptions: ['30分钟', '1小时', '1.5小时', '2小时', '2.5小时', '3小时', '3小时以上'],
      studyTimeIndex: 1,

      // 生成计划状态
      isGeneratingPlan: false,

      // 加载状态
      loading: false
    };
  },

  onLoad() {
    this.initData();
  },

  onShow() {
    this.refreshData();
  },

  methods: {
    // 初始化数据
    async initData() {
      this.loading = true;
      try {
        await Promise.all([
          this.loadPlanStats(),
          this.loadPlanList()
        ]);
      } catch (error) {
        console.error('初始化数据失败:', error);
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 刷新数据
    async refreshData() {
      try {
        await Promise.all([
          this.loadPlanStats(),
          this.loadPlanList()
        ]);
      } catch (error) {
        console.error('刷新数据失败:', error);
      }
    },

    // 加载计划统计
    async loadPlanStats() {
      try {
        const res = await getPlanStats();
        if (res.code === 200) {
          this.planStats = res.data;
        }
      } catch (error) {
        console.error('加载计划统计失败:', error);
      }
    },

    // 加载计划列表
    async loadPlanList() {
      try {
        const res = await getPlanList();
        if (res.code === 200) {
          const plans = res.data.records || [];

          // 分类计划
          this.todayPlans = plans.filter(plan => this.isToday(plan.date) && !plan.completed);
          this.upcomingPlans = plans.filter(plan => this.isUpcoming(plan.date));
          this.completedPlans = plans.filter(plan => plan.completed);
        }
      } catch (error) {
        console.error('加载计划列表失败:', error);
      }
    },

    // 判断是否为今日计划
    isToday(date) {
      const today = new Date();
      const planDate = new Date(date);
      return today.toDateString() === planDate.toDateString();
    },

    // 判断是否为即将到来的计划
    isUpcoming(date) {
      const today = new Date();
      const planDate = new Date(date);
      return planDate > today;
    },

    // 切换标签页
    switchTab(tab) {
      this.activeTab = tab;
    },

    // 切换添加菜单
    toggleAddMenu() {
      this.showAddMenu = !this.showAddMenu;
    },

    // 处理添加计划
    handleAddPlan() {
      this.showAddMenu = false;
      this.resetPlanForm();
      this.$refs.addPlanPopup.open();
    },

    // 处理AI生成计划
    handleAiPlan() {
      this.showAddMenu = false;
      this.resetAiPlanForm();
      this.$refs.aiPlanPopup.open();
    },

    // 重置计划表单
    resetPlanForm() {
      this.planForm = {
        title: '',
        type: 'daily',
        date: '',
        priority: 'medium',
        description: ''
      };
    },

    // 重置AI计划表单
    resetAiPlanForm() {
      this.aiPlanForm = {
        description: '',
        startDate: '',
        learningStyle: '',
        skillLevel: 3,
        studyTime: '',
        otherRequirements: ''
      };
      this.studyTimeIndex = 1;
    },

    // 查看计划详情
    viewPlanDetail(plan) {
      this.selectedPlan = plan;
      this.$refs.planDetailPopup.open();
    },

    // 切换计划状态
    async togglePlanStatus(plan) {
      try {
        const res = await completePlan(plan.planId);
        if (res.code === 200) {
          plan.todayCompleted = true;
          uni.showToast({
            title: '计划已完成',
            icon: 'success'
          });
          this.refreshData();
        }
      } catch (error) {
        console.error('完成计划失败:', error);
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        });
      }
    },

    // 切换子任务状态
    toggleSubtaskStatus(index) {
      if (this.selectedPlan && this.selectedPlan.subTasks) {
        const subtask = this.selectedPlan.subTasks[index];
        subtask.completed = !subtask.completed;
        if (subtask.completed) {
          subtask.completionTime = new Date().toISOString();
        } else {
          subtask.completionTime = null;
        }
      }
    },

    // 日期选择器事件
    onPlanDateChange(e) {
      this.planForm.date = e.detail.value;
    },

    onStartDateChange(e) {
      this.aiPlanForm.startDate = e.detail.value;
    },

    onEditPlanDateChange(e) {
      this.editPlanForm.date = e.detail.value;
    },

    // 学习风格选择
    selectLearningStyle(style) {
      this.aiPlanForm.learningStyle = style;
    },

    // 技能水平变化
    onSkillLevelChange(e) {
      this.aiPlanForm.skillLevel = e.detail.value;
    },

    // 学习时间选择
    onStudyTimeChange(e) {
      this.studyTimeIndex = e.detail.value;
      this.aiPlanForm.studyTime = this.studyTimeOptions[e.detail.value];
    },

    // 保存计划表单
    async savePlanForm() {
      if (!this.validatePlanForm()) {
        return;
      }

      try {
        const res = await createPlan(this.planForm);
        if (res.code === 200) {
          uni.showToast({
            title: '计划创建成功',
            icon: 'success'
          });
          this.closeAddPlanModal();
          this.refreshData();
        }
      } catch (error) {
        console.error('创建计划失败:', error);
        uni.showToast({
          title: '创建失败',
          icon: 'none'
        });
      }
    },

    // 生成AI计划
    async generateAiPlan() {
      if (!this.validateAiPlanForm()) {
        return;
      }

      this.isGeneratingPlan = true;
      try {
        const res = await generateAiPlan(this.aiPlanForm);
        if (res.code === 200) {
          uni.showToast({
            title: 'AI计划生成成功',
            icon: 'success'
          });
          this.closeAiPlanModal();
          this.refreshData();
        }
      } catch (error) {
        console.error('生成AI计划失败:', error);
        uni.showToast({
          title: '生成失败',
          icon: 'none'
        });
      } finally {
        this.isGeneratingPlan = false;
      }
    },

    // 显示编辑计划弹窗
    showEditPlanModal() {
      if (this.selectedPlan) {
        this.editPlanForm = {
          planId: this.selectedPlan.planId,
          title: this.selectedPlan.title,
          type: this.selectedPlan.type,
          date: this.selectedPlan.date,
          priority: this.selectedPlan.priority,
          description: this.selectedPlan.description
        };
        this.closePlanDetailModal();
        this.$refs.editPlanPopup.open();
      }
    },

    // 更新计划表单
    async updatePlanForm() {
      if (!this.validateEditPlanForm()) {
        return;
      }

      try {
        const res = await updatePlan(this.editPlanForm.planId, this.editPlanForm);
        if (res.code === 200) {
          uni.showToast({
            title: '计划更新成功',
            icon: 'success'
          });
          this.closeEditPlanModal();
          this.refreshData();
        }
      } catch (error) {
        console.error('更新计划失败:', error);
        uni.showToast({
          title: '更新失败',
          icon: 'none'
        });
      }
    },

    // 保存计划
    async savePlan() {
      if (this.selectedPlan) {
        try {
          const res = await createPlan(this.selectedPlan);
          if (res.code === 200) {
            uni.showToast({
              title: '计划保存成功',
              icon: 'success'
            });
            this.closePlanDetailModal();
            this.refreshData();
          }
        } catch (error) {
          console.error('保存计划失败:', error);
          uni.showToast({
            title: '保存失败',
            icon: 'none'
          });
        }
      }
    },

    // 验证计划表单
    validatePlanForm() {
      if (!this.planForm.title.trim()) {
        uni.showToast({
          title: '请输入计划标题',
          icon: 'none'
        });
        return false;
      }
      if (!this.planForm.date) {
        uni.showToast({
          title: '请选择日期',
          icon: 'none'
        });
        return false;
      }
      return true;
    },

    // 验证AI计划表单
    validateAiPlanForm() {
      if (!this.aiPlanForm.description.trim()) {
        uni.showToast({
          title: '请输入学习目标',
          icon: 'none'
        });
        return false;
      }
      if (!this.aiPlanForm.startDate) {
        uni.showToast({
          title: '请选择开始日期',
          icon: 'none'
        });
        return false;
      }
      return true;
    },

    // 验证编辑计划表单
    validateEditPlanForm() {
      if (!this.editPlanForm.title.trim()) {
        uni.showToast({
          title: '请输入计划标题',
          icon: 'none'
        });
        return false;
      }
      if (!this.editPlanForm.date) {
        uni.showToast({
          title: '请选择日期',
          icon: 'none'
        });
        return false;
      }
      return true;
    },

    // 关闭弹窗方法
    closeAddPlanModal() {
      this.$refs.addPlanPopup.close();
    },

    closeAiPlanModal() {
      this.$refs.aiPlanPopup.close();
    },

    closePlanDetailModal() {
      this.$refs.planDetailPopup.close();
      this.selectedPlan = null;
    },

    closeEditPlanModal() {
      this.$refs.editPlanPopup.close();
    },

    // 工具方法
    formatDate(date) {
      return formatDate(date);
    },

    formatDateTime(datetime) {
      return formatDateTime(datetime);
    },

    getPriorityText(priority) {
      const priorityMap = {
        'low': '低',
        'medium': '中',
        'high': '高'
      };
      return priorityMap[priority] || '中';
    },

    getPlanTypeText(type) {
      const typeMap = {
        'daily': '日计划',
        'weekly': '周计划',
        'monthly': '月计划'
      };
      return typeMap[type] || '日计划';
    },

    getTimeRange(subTasks) {
      if (!subTasks || subTasks.length === 0) return '';

      const times = subTasks.filter(task => task.startTime && task.endTime);
      if (times.length === 0) return '';

      const startTimes = times.map(task => task.startTime).sort();
      const endTimes = times.map(task => task.endTime).sort();

      return `${startTimes[0]} - ${endTimes[endTimes.length - 1]}`;
    },

    getResourceTypeIcon(type) {
      const iconMap = {
        'video': '📹',
        'article': '📄',
        'book': '📚',
        'course': '🎓',
        'tool': '🔧',
        'website': '🌐'
      };
      return iconMap[type] || '📄';
    },

    // 打开资源
    openResource(resource) {
      if (resource.url) {
        uni.navigateTo({
          url: `/pages/webview/index?url=${encodeURIComponent(resource.url)}&title=${encodeURIComponent(resource.title)}`
        });
      }
    },

    // 导航方法
    navigateToHistory() {
      uni.navigateTo({
        url: '/pages/plan/history'
      });
    },

    navigateToAI() {
      uni.navigateTo({
        url: '/pages/ai/index'
      });
    },

    handleTabClick(index) {
      const routes = [
        '/pages/index/index',
        '/pages/plan/index',
        '/pages/time/index',
        '/pages/study-room/index',
        '/pages/user/index'
      ];

      if (index !== 1) {
        uni.switchTab({
          url: routes[index]
        });
      }
    }
  }
}
</script>

<style>
/* 主容器 */
.plan-container {
  min-height: 100vh;
  background-color: #F8F9FA;
  display: flex;
  flex-direction: column;
}

/* 历史按钮 */
.history-button {
  padding: 8px 16px;
  background-color: rgba(128, 184, 245, 0.1);
  border-radius: 20px;
}

.history-button__text {
  font-size: 14px;
  color: #80B8F5;
  font-weight: 500;
}

/* 主要内容 */
.plan-content {
  flex: 1;
  padding: 0 16px;
}

/* 统计数据区域 */
.plan-stats {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 20px;
  margin: 16px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666666;
}

/* 计划标签页 */
.plan-tabs {
  display: flex;
  flex-direction: row;
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 4px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.plan-tab {
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.plan-tab--active {
  background-color: #80B8F5;
}

.plan-tab__text {
  font-size: 14px;
  color: #666666;
  font-weight: 500;
}

.plan-tab--active .plan-tab__text {
  color: #FFFFFF;
}

/* 计划列表容器 */
.plan-list-container {
  flex: 1;
  margin-bottom: 80px;
}

.plan-section {
  min-height: 200px;
}

.plan-list {
  background-color: #FFFFFF;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-title {
  font-size: 16px;
  color: #333333;
  font-weight: 500;
  margin-bottom: 8px;
}

.empty-text {
  font-size: 14px;
  color: #999999;
  text-align: center;
}

/* 计划行 */
.plan-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #F0F0F0;
  transition: background-color 0.3s;
}

.plan-row:last-child {
  border-bottom: none;
}

.plan-row:active {
  background-color: #F8F9FA;
}

.plan-row--completed {
  opacity: 0.6;
}

/* 复选框 */
.plan-row__checkbox {
  width: 24px;
  height: 24px;
  border: 2px solid #E0E0E0;
  border-radius: 50%;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.plan-row__checkbox--checked {
  background-color: #80B8F5;
  border-color: #80B8F5;
}

.plan-row__checkbox--disabled {
  background-color: #F5F5F5;
  border-color: #E0E0E0;
}

.plan-row__check-icon {
  font-size: 14px;
  color: #FFFFFF;
  font-weight: bold;
}

/* 计划内容 */
.plan-row__body {
  flex: 1;
}

.plan-row__header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.plan-row__title {
  font-size: 16px;
  color: #333333;
  font-weight: 500;
  flex: 1;
  margin-right: 12px;
}

.plan-row__title--completed {
  text-decoration: line-through;
  color: #999999;
}

.plan-row__tags {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.plan-row__tag {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: 4px;
  font-weight: 500;
}

.plan-row__tag--low {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

.plan-row__tag--medium {
  background-color: rgba(255, 193, 7, 0.1);
  color: #FFC107;
}

.plan-row__tag--high {
  background-color: rgba(244, 67, 54, 0.1);
  color: #F44336;
}

.plan-row__tag--type {
  background-color: rgba(128, 184, 245, 0.1);
  color: #80B8F5;
}

.plan-row__info {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.plan-row__date {
  font-size: 12px;
  color: #999999;
  margin-right: 12px;
}

.plan-row__time {
  font-size: 12px;
  color: #666666;
}

/* 浮动添加按钮 */
.floating-button {
  position: fixed;
  right: 20px;
  bottom: 100px;
  width: 56px;
  height: 56px;
  background-color: #80B8F5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(128, 184, 245, 0.4);
  z-index: 100;
}

.floating-button__icon {
  font-size: 24px;
  color: #FFFFFF;
  font-weight: bold;
}

/* 添加菜单 */
.add-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.add-menu-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.add-menu-content {
  position: absolute;
  right: 20px;
  bottom: 170px;
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  min-width: 120px;
}

.add-menu-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 12px 16px;
  border-radius: 8px;
  transition: background-color 0.3s;
}

.add-menu-item:active {
  background-color: #F8F9FA;
}

.add-menu-item__icon {
  font-size: 18px;
  margin-right: 12px;
}

.add-menu-item__text {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
}

/* 计划弹窗 */
.plan-modal {
  width: 90vw;
  max-width: 400px;
  background-color: #FFFFFF;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.plan-modal__header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #F0F0F0;
}

.plan-modal__header.ai-header {
  background: linear-gradient(135deg, #80B8F5 0%, #A8D8EA 100%);
}

.plan-modal__title {
  font-size: 18px;
  color: #333333;
  font-weight: 600;
}

.ai-header .plan-modal__title {
  color: #FFFFFF;
}

.plan-modal__close {
  font-size: 24px;
  color: #999999;
  font-weight: bold;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s;
}

.ai-header .plan-modal__close {
  color: rgba(255, 255, 255, 0.8);
}

.plan-modal__close:active {
  background-color: rgba(0, 0, 0, 0.1);
}

.plan-modal__body {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

.plan-modal__footer {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  padding: 20px;
  border-top: 1px solid #F0F0F0;
  gap: 12px;
}

/* AI介绍 */
.ai-intro {
  background: linear-gradient(135deg, rgba(128, 184, 245, 0.1) 0%, rgba(168, 216, 234, 0.1) 100%);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 20px;
  border: 1px solid rgba(128, 184, 245, 0.2);
}

.ai-intro__text {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
  margin-bottom: 8px;
  display: block;
}

.ai-intro__note {
  font-size: 12px;
  color: #666666;
  display: block;
}

/* 表单项 */
.form-item {
  margin-bottom: 20px;
}

.form-label {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
  margin-bottom: 8px;
  display: block;
}

.form-label.required::after {
  content: '*';
  color: #F44336;
  margin-left: 4px;
}

.form-input {
  width: 100%;
  height: 44px;
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  padding: 0 12px;
  font-size: 14px;
  color: #333333;
  background-color: #FFFFFF;
}

.form-input:focus {
  border-color: #80B8F5;
  outline: none;
}

.form-textarea {
  width: 100%;
  min-height: 80px;
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  color: #333333;
  background-color: #FFFFFF;
  resize: vertical;
}

.form-textarea:focus {
  border-color: #80B8F5;
  outline: none;
}

.picker-item {
  height: 44px;
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  padding: 0 12px;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333333;
  background-color: #FFFFFF;
}

/* 计划类型选择器 */
.plan-type-selector {
  display: flex;
  flex-direction: row;
  gap: 8px;
}

.plan-type-item {
  flex: 1;
  height: 40px;
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.plan-type-item--active {
  background-color: #80B8F5;
  border-color: #80B8F5;
}

.plan-type-item--disabled {
  opacity: 0.5;
  pointer-events: none;
}

.plan-type-item__text {
  font-size: 14px;
  color: #666666;
  font-weight: 500;
}

.plan-type-item--active .plan-type-item__text {
  color: #FFFFFF;
}
</style>