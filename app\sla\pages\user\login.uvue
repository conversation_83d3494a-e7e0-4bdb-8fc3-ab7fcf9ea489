<template>
  <view class="login-container">
    <!-- 顶部装饰 -->
    <view class="bottom-decoration"></view>

    <!-- 登录Logo和标题 -->
    <view class="login-header">
      <image class="login-logo" src="/static/images/logo.png" mode="aspectFit"></image>
      <text class="login-title">智能学习助手</text>
      <text class="login-subtitle">让学习更轻松，效率更高</text>
    </view>

    <!-- 登录选项卡 -->
    <view class="login-tabs">
      <view
        class="login-tab"
        :class="{ 'login-tab--active': loginType === 'code' }"
        @click="switchLoginType('code')"
      >
        <text class="login-tab__text" :class="{ 'login-tab__text--active': loginType === 'code' }">验证码登录</text>
      </view>
      <view
        class="login-tab"
        :class="{ 'login-tab--active': loginType === 'password' }"
        @click="switchLoginType('password')"
      >
        <text class="login-tab__text" :class="{ 'login-tab__text--active': loginType === 'password' }">密码登录</text>
      </view>
    </view>

    <!-- 验证码登录表单 -->
    <view v-if="loginType === 'code'" class="login-form">
      <!-- 手机号输入 -->
      <view class="input-item">
        <text class="input-label">手机号 <text class="required">*</text></text>
        <view class="phone-input-container">
          <view class="phone-prefix">+86</view>
          <input
            class="input-field phone-input"
            type="number"
            v-model="codeForm.phone as string"
            placeholder="请输入手机号"
            maxlength="11"
            @input="validateCodePhone"
          />
        </view>
        <text v-if="codeFormErrors.phone" class="error-text">{{codeFormErrors.phone}}</text>
      </view>

      <!-- 验证码输入 -->
      <view class="input-item">
        <text class="input-label">验证码 <text class="required">*</text></text>
        <view class="verification-code-container">
          <input
            class="input-field verification-code-input"
            type="number"
            v-model="codeForm.code as string"
            placeholder="请输入验证码"
            maxlength="6"
            @input="validateCode"
          />
          <view
            class="verification-code-button"
            :class="{'verification-code-button--active': isPhoneValid, 'verification-code-button--disabled': !isPhoneValid || counting}"
            @click="sendVerificationCode"
          >
            <text class="verification-code-text">{{countdownText}}</text>
          </view>
        </view>
        <text v-if="codeFormErrors.code" class="error-text">{{codeFormErrors.code}}</text>
      </view>

      <!-- 登录按钮 -->
      <view class="login-actions">
        <view></view>
        <view></view>
      </view>

      <button
        class="login-button"
        :class="{'login-button--disabled': !isCodeLoginEnabled}"
        :disabled="!isCodeLoginEnabled"
        @click="handleCodeLogin"
      >
        <text v-if="!codeLoading" class="login-button-text">登录</text>
        <view v-else class="loading-icon"></view>
      </button>
    </view>

    <!-- 密码登录表单 -->
    <view v-else class="login-form">
      <!-- 手机号输入 -->
      <view class="input-item">
        <text class="input-label">手机号 <text class="required">*</text></text>
        <view class="phone-input-container">
          <view class="phone-prefix">+86</view>
          <input
            class="input-field phone-input"
            type="number"
            v-model="passwordForm.phone as string"
            placeholder="请输入手机号"
            maxlength="11"
            @input="validatePasswordPhone"
          />
        </view>
        <text v-if="passwordFormErrors.phone" class="error-text">{{passwordFormErrors.phone}}</text>
      </view>

      <!-- 密码输入 -->
      <view class="input-item">
        <text class="input-label">密码 <text class="required">*</text></text>
        <input
          class="input-field"
          :type="showPassword ? 'text' : 'password'"
          v-model="passwordForm.password as string"
          placeholder="请输入密码"
          @input="validatePassword"
        />
        <text v-if="passwordFormErrors.password" class="error-text">{{passwordFormErrors.password}}</text>
      </view>

      <!-- 登录按钮 -->
      <view class="login-actions">
        <view></view>
        <text class="login-forgot" @click="navigateToForgetPassword">忘记密码？</text>
      </view>

      <button
        class="login-button"
        :class="{'login-button--disabled': !isPasswordLoginEnabled}"
        :disabled="!isPasswordLoginEnabled"
        @click="handlePasswordLogin"
      >
        <text v-if="!passwordLoading" class="login-button-text">登录</text>
        <view v-else class="loading-icon"></view>
      </button>
    </view>

    <!-- 注册提示 -->
    <view class="login-register">
      <text class="login-register__text">没有账号？</text>
      <text class="login-register__link" @click="navigateToRegister">注册</text>
    </view>

    <!-- 底部信息 -->
    <view class="login-footer">
      <text class="login-footer__text">© 2024 智能学习助手</text>
      <text class="login-footer__link">隐私政策</text>
      <text class="login-footer__text">|</text>
      <text class="login-footer__link">帮助</text>
    </view>
  </view>
</template>

<script>
import { login, sendSms } from '../../utils/api/user.js';

export default {
  components: {
  },
  data() {
    return {
      // 登录类型，'code'表示验证码登录，'password'表示密码登录
      loginType: 'code',

      // 验证码登录表单数据
      codeForm: {
        phone: '',
        code: '',
        deviceInfo: ''
      },

      // 密码登录表单数据
      passwordForm: {
        phone: '',
        password: '',
        deviceInfo: ''
      },

      // 验证码登录表单错误信息
      codeFormErrors: {
        phone: '',
        code: ''
      },

      // 密码登录表单错误信息
      passwordFormErrors: {
        phone: '',
        password: ''
      },

      // 验证码登录加载状态
      codeLoading: false,

      // 密码登录加载状态
      passwordLoading: false,

      // 是否显示密码
      showPassword: false,

      // 倒计时相关
      counting: false,
      countdownSeconds: 60,
      remainingSeconds: 60,

      // 验证码发送状态
      codeSent: false
    };
  },
  computed: {
    // 验证码登录手机号验证
    isPhoneValid() {
      return this.codeForm.phone && /^1[3-9]\d{9}$/.test(this.codeForm.phone);
    },

    // 验证码登录表单验证
    isCodeLoginEnabled() {
      return this.isPhoneValid && this.codeForm.code && this.codeForm.code.length === 6;
    },

    // 密码登录表单验证
    isPasswordLoginEnabled() {
      return this.passwordForm.phone &&
             /^1[3-9]\d{9}$/.test(this.passwordForm.phone) &&
             this.passwordForm.password &&
             this.passwordForm.password.length >= 6;
    },

    // 倒计时文本
    countdownText() {
      if (this.counting) {
        return `${this.remainingSeconds}秒后重试`;
      }
      return '获取验证码';
    }
  },
  onLoad() {
    // 获取设备信息
    this.getDeviceInfo();
  },
  methods: {
    // 切换登录类型
    switchLoginType(type) {
      this.loginType = type;

      // 重置表单错误信息
      if (type === 'code') {
        this.codeFormErrors.phone = '';
        this.codeFormErrors.code = '';
      } else {
        this.passwordFormErrors.phone = '';
        this.passwordFormErrors.password = '';
      }

      console.log('切换登录类型:', type);
    },

    // 验证码登录手机号验证
    validateCodePhone() {
      if (!this.codeForm.phone) {
        this.codeFormErrors.phone = '';
        return;
      }

      if (!/^1[3-9]\d{9}$/.test(this.codeForm.phone)) {
        this.codeFormErrors.phone = '请输入正确的手机号';
      } else {
        this.codeFormErrors.phone = '';
      }
    },

    // 验证码登录验证码验证
    validateCode() {
      if (!this.codeForm.code) {
        this.codeFormErrors.code = '';
        return;
      }

      if (!/^\d{6}$/.test(this.codeForm.code)) {
        this.codeFormErrors.code = '请输入正确的验证码';
      } else {
        this.codeFormErrors.code = '';
      }
    },

    // 密码登录手机号验证
    validatePasswordPhone() {
      if (!this.passwordForm.phone) {
        this.passwordFormErrors.phone = '';
        return;
      }

      if (!/^1[3-9]\d{9}$/.test(this.passwordForm.phone)) {
        this.passwordFormErrors.phone = '请输入正确的手机号';
      } else {
        this.passwordFormErrors.phone = '';
      }
    },

    // 密码登录密码验证
    validatePassword() {
      if (!this.passwordForm.password) {
        this.passwordFormErrors.password = '';
        return;
      }

      if (this.passwordForm.password.length < 6 || this.passwordForm.password.length > 20) {
        this.passwordFormErrors.password = '密码长度应在6-20个字符之间';
      } else if (!/^(?=.*[A-Za-z])(?=.*\d)[\w\W]{6,20}$/.test(this.passwordForm.password)) {
        this.passwordFormErrors.password = '密码必须包含字母和数字';
      } else {
        this.passwordFormErrors.password = '';
      }
    },

    // 发送验证码
    sendVerificationCode() {
      console.log('发送验证码请求:', this.codeForm.phone, '验证:', this.isPhoneValid);

      // 如果手机号无效或正在倒计时，则不发送
      if (!this.isPhoneValid || this.counting) {
        return;
      }

      // 显示加载中
      uni.showLoading({
        title: '发送中...'
      });

      // 发送验证码请求
      sendSms(this.codeForm.phone, 1).then(() => {
        uni.hideLoading();
        uni.showToast({
          title: '验证码已发送',
          icon: 'success'
        });

        // 设置验证码发送状态
        this.codeSent = true;

        // 开始倒计时
        this.startCountdown();
      }).catch(err => {
        uni.hideLoading();
        console.error('发送验证码失败:', err);
        uni.showToast({
          title: err.message || '发送验证码失败',
          icon: 'none'
        });
      });
    },

    // 开始倒计时
    startCountdown() {
      if (this.counting) return;

      this.counting = true;
      this.remainingSeconds = this.countdownSeconds;

      const timer = setInterval(() => {
        this.remainingSeconds--;

        if (this.remainingSeconds <= 0) {
          clearInterval(timer);
          this.counting = false;
        }
      }, 1000);
    },

    // 验证码登录处理
    handleCodeLogin() {
      // 手机号无效
      if (!this.isPhoneValid) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        });
        return;
      }

      if (!this.codeForm.code || this.codeForm.code.length !== 6) {
        uni.showToast({
          title: '请输入正确的验证码',
          icon: 'none'
        });
        return;
      }

      this.codeLoading = true;

      // 构造登录参数
      const loginParams = {
        loginType: 1, // 登录类型
        phone: this.codeForm.phone,
        code: this.codeForm.code,
        deviceInfo: this.codeForm.deviceInfo
      };

      // 发送登录请求
      login(loginParams).then(res => {
        this.codeLoading = false;

        // 清除登录需要
        uni.removeStorageSync('needLogin');
        
        // 打印返回数据
        console.log('登录成功:', JSON.stringify(res.data));
        
        // 延迟获取用户信息
        setTimeout(() => {
          const storedUserInfo = uni.getStorageSync('userInfo');
          console.log('用户信息:', JSON.stringify(storedUserInfo));
          console.log('用户ID:', {
            id: storedUserInfo?.id,
            userId: storedUserInfo?.userId
          });
        }, 100);

        uni.showToast({
          title: '登录成功',
          icon: 'success',
          duration: 1500
        });

        // 打印登录成功
        console.log('登录成功');
        // 使用reLaunch跳转到tabBar页面
        uni.reLaunch({
          url: '/pages/index/index'
        });
      }).catch(err => {
        this.codeLoading = false;
        uni.showToast({
          title: err.message || '登录失败',
          icon: 'none'
        });
      });
    },

    // 密码登录处理
    handlePasswordLogin() {
      // 手机号无效
      if (!this.passwordForm.phone || !/^1[3-9]\d{9}$/.test(this.passwordForm.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        });
        return;
      }

      if (!this.passwordForm.password || this.passwordForm.password.length < 6) {
        uni.showToast({
          title: '请输入正确的密码',
          icon: 'none'
        });
        return;
      }

      if (!/^(?=.*[A-Za-z])(?=.*\d)[\w\W]{6,20}$/.test(this.passwordForm.password)) {
        uni.showToast({
          title: '密码必须包含字母和数字',
          icon: 'none'
        });
        return;
      }

      this.passwordLoading = true;

      // 构造登录参数
      const loginParams = {
        loginType: 2, // 登录类型
        phone: this.passwordForm.phone,
        password: this.passwordForm.password,
        deviceInfo: this.passwordForm.deviceInfo
      };

      // 发送登录请求
      login(loginParams).then(res => {
        this.passwordLoading = false;

        // 清除登录需要
        uni.removeStorageSync('needLogin');
        
        // 打印返回数据
        console.log('登录成功:', JSON.stringify(res.data));
        
        // 延迟获取用户信息
        setTimeout(() => {
          const storedUserInfo = uni.getStorageSync('userInfo');
          console.log('用户信息:', JSON.stringify(storedUserInfo));
          console.log('用户ID:', {
            id: storedUserInfo?.id,
            userId: storedUserInfo?.userId
          });
        }, 100);

        uni.showToast({
          title: '登录成功',
          icon: 'success',
          duration: 1500
        });

        // 打印登录成功
        console.log('登录成功');
        // 使用reLaunch跳转到tabBar页面
        uni.reLaunch({
          url: '/pages/index/index'
        });
      }).catch(err => {
        this.passwordLoading = false;
        uni.showToast({
          title: err.message || '登录失败',
          icon: 'none'
        });
      });
    },

    // 忘记密码处理
    navigateToForgetPassword() {
      uni.navigateTo({
        url: '/pages/user/forget-password'
      });
    },

    // 注册处理
    navigateToRegister() {
      uni.navigateTo({
        url: '/pages/user/register'
      });
    },

    // 获取设备信息
    getDeviceInfo() {
      try {
        const systemInfo = uni.getSystemInfoSync();
        const deviceInfo = `${systemInfo.brand || ''} ${systemInfo.model || ''}`;
        this.codeForm.deviceInfo = deviceInfo;
        this.passwordForm.deviceInfo = deviceInfo;
      } catch (e) {
        const deviceInfo = 'Unknown Device';
        this.codeForm.deviceInfo = deviceInfo;
        this.passwordForm.deviceInfo = deviceInfo;
      }
    }
  }
}
</script>

<style>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #4361ee 0%, #3a86ff 100%); /* 渐变背景 */
  padding: 20px 0 0 0; /* 顶部填充 */
  position: relative;
  overflow: hidden;
}

/* 顶部装饰 */
.login-container::before {
  content: "";
  position: absolute;
  top: -100px;
  right: -100px;
  width: 350px;
  height: 350px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
  z-index: 0;
  animation: float 15s ease-in-out infinite alternate;
}

.login-container::after {
  content: "";
  position: absolute;
  bottom: -100px;
  left: -100px;
  width: 300px;
  height: 300px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
  z-index: 0;
  animation: float 20s ease-in-out infinite alternate-reverse;
}

/* 底部装饰 - 使用伪元素的另一种方式 */
.login-container .bottom-decoration {
  content: "";
  position: fixed;
  bottom: 30%;
  right: -50px;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(138, 43, 226, 0.1), rgba(255, 255, 255, 0.05));
  z-index: 0;
  animation: float 18s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translate(0, 0) scale(1);
  }
  50% {
    transform: translate(-20px, 20px) scale(1.05);
  }
  100% {
    transform: translate(20px, -20px) scale(0.95);
  }
}

.login-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 40px; /* 顶部填充 */
  margin-bottom: 30px;
  position: relative;
  z-index: 1;
  padding: 0 20px;
  animation: float-slow 6s ease-in-out infinite;
}

.login-logo {
  width: 80px;
  height: 80px;
  margin-bottom: 15px;
  border-radius: 22px;
  background-color: rgba(255, 255, 255, 0.25);
  padding: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.2);
  animation: pulse-glow 3s ease-in-out infinite alternate;
}

@keyframes float-slow {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse-glow {
  0% { box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.2); }
  100% { box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.3), 0 0 20px rgba(255, 255, 255, 0.3); }
}

.login-title {
  font-size: 24px;
  font-weight: bold;
  color: #FFFFFF;
  margin-bottom: 5px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.login-subtitle {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.login-tabs {
  display: flex;
  flex-direction: row;
  margin-bottom: 20px;
  width: 90%;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  z-index: 1;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  padding: 5px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  animation: gradient-shift 8s ease infinite;
  overflow: hidden;
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.login-tab {
  flex: 1;
  padding: 12px 0;
  position: relative;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex;
  justify-content: center;
  overflow: hidden;
  z-index: 2;
  border-radius: 12px;
}

.login-tab:hover {
  transform: translateY(-2px);
  background-color: rgba(255, 255, 255, 0.1);
}

.login-tab:first-child {
  justify-content: center;
  margin-right: 2px;
}

.login-tab:last-child {
  justify-content: center;
  margin-left: 2px;
}

.login-tab--active {
  background-color: rgba(255, 255, 255, 0.25);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.login-tab--active::after {
  content: none;
}

.login-tab__text {
  font-size: 15px;
  color: rgba(255, 255, 255, 0.7);
  padding: 0 10px;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.login-tab:hover .login-tab__text {
  color: rgba(255, 255, 255, 0.9);
  transform: scale(1.05);
}

.login-tab__text--active {
  color: #FFFFFF;
  font-weight: 600;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.login-form {
  background-color: rgba(255, 255, 255, 0.5); /* 半透明背景 */
  backdrop-filter: blur(15px); /* 模糊背景 */
  border-radius: 24px; /* 圆角 */
  padding: 24px;
  margin: 0 20px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(138, 43, 226, 0.15); /* 阴影 */
  position: relative;
  z-index: 1;
  animation: form-fade-in 0.5s ease-out;
  min-height: 240px; /* 最小高度 */
  border: 1.5px solid rgba(255, 255, 255, 0.5); /* 边框 */
  background-image: 
    linear-gradient(135deg, rgba(138, 43, 226, 0.05), rgba(255, 255, 255, 0.3), rgba(138, 43, 226, 0.05)),
    radial-gradient(circle at 10% 10%, rgba(255, 255, 255, 0.2), transparent 70%); /* 渐变 */
  overflow: hidden; /* 溢出隐藏 */
}

/* 顶部装饰 */
.login-form::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2) 0%, transparent 60%);
  opacity: 0.6;
  animation: rotate 20s linear infinite;
  pointer-events: none;
  z-index: -1;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes form-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.input-item {
  margin-bottom: 16px;
}

.input-label {
  font-size: 13px;
  color: rgba(138, 43, 226, 0.8); /* 标签颜色 */
  margin-bottom: 6px;
  display: block;
  font-weight: 500;
  letter-spacing: 0.5px; /* 字间距 */
  transition: all 0.3s ease; /* 过渡 */
}

.required {
  color: #FF6B6B;
}

.input-item {
  position: relative;
  margin-bottom: 16px;
}

.input-field {
  width: 100%;
  height: 48px; /* 输入框高度 */
  border: 1px solid rgba(138, 43, 226, 0.4); /* 边框 */
  border-radius: 12px; /* 圆角 */
  padding: 0 16px;
  font-size: 15px;
  color: #333333;
  background-color: rgba(248, 249, 250, 0.8); /* 背景颜色 */
  transition: all 0.3s ease;
  box-sizing: border-box;
  backdrop-filter: blur(5px); /* 模糊效果 */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1), inset 0 1px 2px rgba(255, 255, 255, 0.9); /* 阴影 */
  font-weight: 500; /* 字体粗细 */
}

.input-field:focus {
  outline: none;
  border: 2px solid #8A2BE2; /* 边框颜色 */
  box-shadow: 0 0 12px rgba(138, 43, 226, 0.5), inset 0 1px 3px rgba(255, 255, 255, 1);
  background-color: rgba(255, 255, 255, 0.9); /* 背景颜色 */
  transform: translateY(-1px); /* 移动 */
}

/* 输入框悬停效果 */
.input-field:hover {
  border-color: rgba(138, 43, 226, 0.6);
  background-color: rgba(250, 250, 252, 0.95);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.12), inset 0 1px 3px rgba(255, 255, 255, 0.9);
}

.error-text {
  font-size: 12px;
  color: #FF6B6B;
  margin-top: 4px;
}

.verification-code-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
}

.verification-code-input {
  flex: 1;
}

.verification-code-button {
  height: 44px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  background-color: rgba(240, 244, 255, 0.7); /* 背景颜色 */
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(138, 43, 226, 0.3); /* 边框 */
  backdrop-filter: blur(5px); /* 模糊效果 */
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05); /* 阴影 */
}

.verification-code-button--active {
  background-color: #8A2BE2; /* 背景颜色 */
  box-shadow: 0 4px 10px rgba(138, 43, 226, 0.2);
  border: none;
}

.verification-code-button--disabled {
  opacity: 0.6; /* 透明度 */
  pointer-events: none;
  background-color: rgba(240, 244, 255, 0.8);
  border: 1px solid rgba(138, 43, 226, 0.15); /* 边框 */
}

.verification-code-text {
  font-size: 13px;
  color: rgba(138, 43, 226, 0.7); /* 文本颜色 */
  white-space: nowrap;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
  font-weight: 500; /* 字体粗细 */
}

.verification-code-button--active .verification-code-text {
  color: #FFFFFF;
  font-weight: 500;
}

.login-actions {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin: 12px 0;
}

.login-forgot {
  font-size: 13px;
  color: #8A2BE2;
  font-weight: 500;
}

.login-button {
  width: 100%;
  height: 50px; /* 高度 */
  background: linear-gradient(135deg, #8A2BE2, #9370DB);
  border-radius: 14px; /* 圆角 */
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 25px;
  box-shadow: 0 6px 15px rgba(138, 43, 226, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.1) inset; /* 阴影 */
  transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1); /* 过渡 */
  position: relative;
  overflow: hidden;
  border: none;
}

/* 登录按钮悬停效果 */
.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.4s ease;
}

.login-button:hover::before {
  left: 100%;
}

.login-button:active {
  transform: translateY(2px);
  box-shadow: 0 2px 5px rgba(138, 43, 226, 0.3);
}

.login-button--disabled {
  background: linear-gradient(135deg, #b9aac5, #d0c2dc); /* 禁用背景 */
  box-shadow: 0 2px 5px rgba(138, 43, 226, 0.1); /* 禁用阴影 */
  opacity: 0.8;
  border: 1px solid rgba(138, 43, 226, 0.1); /* 禁用边框 */
}

.login-button-text {
  font-size: 15px;
  color: #FFFFFF;
  font-weight: 600;
  position: relative;
  z-index: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.loading-icon {
  width: 20px;
  height: 20px;
  border: 2px solid #FFFFFF;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.login-register {
  display: flex;
  flex-direction: row;
  justify-content: center;
  margin-top: 20px;
  position: relative;
  z-index: 1;
}

.login-register__text {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
}

.login-register__link {
  font-size: 13px;
  color: #FFFFFF;
  margin-left: 4px;
  font-weight: 600;
  text-decoration: none; /* 去掉下划线 */
  padding-bottom: 2px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.5); /* 底部边框 */
  transition: all 0.3s ease;
}

.login-register__link:hover {
  border-bottom-color: #FFFFFF;
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.6); /* 文本阴影 */
}

.login-footer {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  margin-top: 20px;
  padding: 0 20px 20px;
  position: relative;
  z-index: 1;
}

.login-footer__text {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
}

.login-footer__link {
  font-size: 11px;
  color: #FFFFFF;
  margin: 0 2px;
  text-decoration: underline;
}

/* 手机号输入框样式 */
.phone-input-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  position: relative;
}

.phone-prefix {
  position: absolute;
  left: 12px;
  color: #333;
  font-size: 15px;
  font-weight: 500;
  z-index: 2;
  background-color: rgba(248, 249, 250, 0.8);
  padding: 0 4px;
  border-right: 1px solid rgba(138, 43, 226, 0.2);
  height: 24px;
  line-height: 24px;
  display: flex;
  align-items: center;
}

.phone-input {
  padding-left: 52px !important; /* 左侧填充 */
}
</style>